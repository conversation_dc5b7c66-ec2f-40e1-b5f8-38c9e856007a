# 数据库连接配置指南

## 概述
本文档详细说明如何使用Navicat Premium Lite 17连接Docker容器中的数据库服务，并配置Spring Boot应用的数据库连接。

## 第一步：验证Docker容器状态

首先确认所有容器都在正常运行：

```bash
# 查看容器状态
docker ps

# 应该看到三个容器都在运行：
# - mysql-dev (或 my-mysql)
# - mongodb-dev (或 my-mongo) 
# - redis-dev (或 my-redis)
```

## 第二步：使用Navicat连接数据库

### 2.1 连接MySQL数据库

#### 创建MySQL连接
1. 打开Navicat Premium Lite 17
2. 点击"连接" → "MySQL"
3. 填写连接信息：

| 配置项 | 值 | 说明 |
|--------|-----|------|
| 连接名 | Docker-MySQL-Dev | 自定义连接名称 |
| 主机 | localhost | Docker映射到本机 |
| 端口 | 3306 | MySQL标准端口 |
| 用户名 | root | 管理员用户 |
| 密码 | root123456 | 容器创建时设置的密码 |

4. 点击"测试连接"验证连接
5. 连接成功后点击"确定"保存

#### 创建应用数据库
1. 右键点击MySQL连接 → "打开连接"
2. 右键点击连接名 → "新建数据库"
3. 填写数据库信息：
   - **数据库名**: `sb_free_music`
   - **字符集**: `utf8mb4`
   - **排序规则**: `utf8mb4_bin`
4. 点击"确定"创建数据库

#### 创建应用用户（推荐）
为了安全性，建议创建专门的应用用户：

```sql
-- 创建应用用户
CREATE USER 'sb_music_user'@'%' IDENTIFIED BY 'SbMusic@2024';

-- 授权数据库权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX 
ON sb_free_music.* TO 'sb_music_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 2.2 连接MongoDB数据库

#### 创建MongoDB连接
1. 在Navicat中点击"连接" → "MongoDB"
2. 填写连接信息：

| 配置项 | 值 | 说明 |
|--------|-----|------|
| 连接名 | Docker-MongoDB-Dev | 自定义连接名称 |
| 主机 | localhost | Docker映射到本机 |
| 端口 | 27017 | MongoDB标准端口 |
| 认证数据库 | admin | 管理数据库 |
| 用户名 | admin | 管理员用户 |
| 密码 | @JJ193495.x | 容器创建时设置的密码 |

3. 点击"测试连接"验证连接
4. 连接成功后点击"确定"保存

#### 创建应用数据库
1. 右键点击MongoDB连接 → "打开连接"
2. 右键点击连接名 → "新建数据库"
3. 数据库名: `sb_free_music`
4. 点击"确定"创建数据库

### 2.3 连接Redis数据库

#### 创建Redis连接
1. 在Navicat中点击"连接" → "Redis"
2. 填写连接信息：

| 配置项 | 值 | 说明 |
|--------|-----|------|
| 连接名 | Docker-Redis-Dev | 自定义连接名称 |
| 主机 | localhost | Docker映射到本机 |
| 端口 | 6379 | Redis标准端口 |
| 密码 | @JJ193495.x | Redis访问密码 |

3. 点击"测试连接"验证连接
4. 连接成功后点击"确定"保存

## 第三步：更新Spring Boot配置

### 3.1 更新application.yaml配置

将原有的配置更新为Docker环境配置：

```yaml
spring:
  application:
    name: sb_free_music

  # ===== 数据源配置 =====
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 使用专用应用用户（推荐）
    username: ${DB_USERNAME:sb_music_user}
    password: ${DB_PASSWORD:SbMusic@2024}
    # 或者使用root用户（开发环境）
    # username: ${DB_USERNAME:root}
    # password: ${DB_PASSWORD:root123456}
    
    # Docker MySQL连接URL
    url: **********************************************************************************************************************************************************
    
    # 连接池配置（HikariCP）
    hikari:
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接池大小
      maximum-pool-size: 20
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲连接最大存活时间（毫秒）
      idle-timeout: 600000
      # 连接最大存活时间（毫秒）
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1

  # ===== Redis配置 =====
  data:
    redis:
      # Docker Redis连接配置
      host: localhost
      port: 6379
      password: "@JJ193495.x"
      # 连接超时时间（毫秒）
      timeout: 5000
      # 数据库索引（默认0）
      database: 0
      
      # 连接池配置（Lettuce）
      lettuce:
        pool:
          # 最大连接数
          max-active: 20
          # 最大空闲连接数
          max-idle: 10
          # 最小空闲连接数
          min-idle: 5
          # 获取连接最大等待时间（毫秒）
          max-wait: 5000

  # ===== MongoDB配置（如果使用） =====
  data:
    mongodb:
      # Docker MongoDB连接配置
      host: localhost
      port: 27017
      database: sb_free_music
      username: admin
      password: "@JJ193495.x"
      authentication-database: admin
      
      # 连接池配置
      options:
        # 最大连接数
        connections-per-host: 20
        # 最小连接数
        min-connections-per-host: 5
        # 连接超时时间（毫秒）
        connect-timeout: 5000
        # Socket超时时间（毫秒）
        socket-timeout: 5000

# ===== MyBatis-Plus配置 =====
mybatis-plus:
  # 实体类包路径
  type-aliases-package: org.example.sb_free_music.model.entity
  
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型 AUTO:数据库自增 ASSIGN_ID:雪花算法
      id-type: AUTO
      # 表名前缀
      table-prefix: t_
      # 逻辑删除字段名
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  
  # MyBatis配置项
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 关闭积极延迟加载
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许JDBC支持自动生成主键
    use-generated-keys: true
    # 自动映射行为 PARTIAL:部分映射
    auto-mapping-behavior: PARTIAL
    # 未知列映射行为 WARNING:警告
    auto-mapping-unknown-column-behavior: WARNING
    # 默认执行器类型 SIMPLE:简单执行器
    default-executor-type: SIMPLE
    # 默认语句超时时间（秒）
    default-statement-timeout: 25
    # 默认获取数据量
    default-fetch-size: 100
    # 日志实现 - 开发环境输出SQL到控制台
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# ===== 日志配置 =====
logging:
  level:
    # 应用日志级别
    org.example.sb_free_music: DEBUG
    # MyBatis日志级别
    org.apache.ibatis: DEBUG
    # SQL日志级别
    org.apache.ibatis.logging.jdbc: DEBUG
    # HikariCP连接池日志
    com.zaxxer.hikari: DEBUG
    # Redis日志级别
    org.springframework.data.redis: DEBUG

# ===== 服务器配置 =====
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
```

### 3.2 环境变量配置（推荐）

为了安全性，建议使用环境变量管理敏感信息。创建 `.env` 文件（不要提交到版本控制）：

```properties
# 数据库配置
DB_USERNAME=sb_music_user
DB_PASSWORD=SbMusic@2024

# Redis配置
REDIS_PASSWORD=@JJ193495.x

# MongoDB配置
MONGO_USERNAME=admin
MONGO_PASSWORD=@JJ193495.x
```

## 第四步：验证连接

### 4.1 创建测试控制器

创建一个简单的测试控制器验证数据库连接：

```java
@RestController
@RequestMapping("/test")
public class DatabaseTestController {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @GetMapping("/mysql")
    public String testMySQL() {
        try (Connection connection = dataSource.getConnection()) {
            return "MySQL连接成功！数据库：" + connection.getCatalog();
        } catch (Exception e) {
            return "MySQL连接失败：" + e.getMessage();
        }
    }
    
    @GetMapping("/redis")
    public String testRedis() {
        try {
            redisTemplate.opsForValue().set("test", "Hello Redis!");
            String value = redisTemplate.opsForValue().get("test");
            return "Redis连接成功！测试值：" + value;
        } catch (Exception e) {
            return "Redis连接失败：" + e.getMessage();
        }
    }
}
```

### 4.2 启动应用测试

1. 启动Spring Boot应用
2. 访问测试接口：
   - MySQL测试：`http://localhost:8080/api/test/mysql`
   - Redis测试：`http://localhost:8080/api/test/redis`

## 配置说明

### 主要变更点：
1. **时区设置**：从UTC改为Asia/Shanghai
2. **字符编码**：明确指定utf8mb4
3. **连接池配置**：添加HikariCP和Lettuce连接池配置
4. **安全性**：添加密码配置和专用用户
5. **监控友好**：添加详细的日志配置

### 性能优化：
1. **连接池大小**：根据应用负载调整
2. **超时设置**：避免长时间等待
3. **缓存配置**：启用MyBatis二级缓存
4. **日志级别**：生产环境建议调整为INFO或WARN

## 注意事项

1. **防火墙**：确保Windows防火墙允许相应端口访问
2. **密码安全**：生产环境请使用更复杂的密码
3. **连接数限制**：注意数据库的最大连接数限制
4. **备份策略**：定期备份重要数据
5. **监控告警**：建议配置数据库连接监控

配置完成后，您的Spring Boot应用就可以正常连接Docker容器中的数据库服务了！
