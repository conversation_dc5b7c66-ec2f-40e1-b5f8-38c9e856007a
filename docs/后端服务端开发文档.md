# Free Music 后端服务端开发文档

## 项目概述
基于Spring Boot 3的音乐网站后端服务，提供用户管理、音乐管理、评论系统、消息推送等功能。

## 技术栈
- **框架**: Spring Boot 3.5.3
- **数据库**: MySQL 8.0+ (主数据库)
- **NoSQL**: MongoDB (播放记录存储)
- **缓存**: Redis (会话管理、缓存)
- **ORM**: MyBatis-Plus 3.5.5
- **安全**: Spring Security + JWT
- **实时通信**: WebSocket
- **Java版本**: JDK 21

## 数据库设计

### MySQL 主数据库表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密密码',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    user_type ENUM('NORMAL', 'VIP', 'SVIP') DEFAULT 'NORMAL' COMMENT '用户类型',
    user_level VARCHAR(10) DEFAULT 'K1' COMMENT '用户等级',
    user_title VARCHAR(50) DEFAULT '初出茅庐' COMMENT '用户称号',
    total_listen_time BIGINT DEFAULT 0 COMMENT '总听歌时间(秒)',
    account_status ENUM('NORMAL', 'BANNED') DEFAULT 'NORMAL' COMMENT '账号状态',
    ban_end_time DATETIME COMMENT '封禁结束时间',
    qq_openid VARCHAR(100) COMMENT 'QQ登录OpenID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
);
```

#### 2. 歌手表 (artists)
```sql
CREATE TABLE artists (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '歌手名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    description TEXT COMMENT '歌手简介',
    country VARCHAR(50) COMMENT '国家/地区',
    birth_date DATE COMMENT '出生日期',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);
```

#### 3. 歌曲分类表 (categories)
```sql
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    description VARCHAR(200) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);
```

#### 4. 歌曲表 (songs)
```sql
CREATE TABLE songs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '歌曲标题',
    artist_id BIGINT NOT NULL COMMENT '歌手ID',
    category_id BIGINT COMMENT '分类ID',
    album VARCHAR(200) COMMENT '专辑名',
    duration INT NOT NULL COMMENT '时长(秒)',
    file_url VARCHAR(500) NOT NULL COMMENT '音频文件URL',
    cover_url VARCHAR(500) COMMENT '封面图URL',
    lyrics TEXT COMMENT '歌词',
    play_count BIGINT DEFAULT 0 COMMENT '播放次数',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',
    download_count BIGINT DEFAULT 0 COMMENT '下载次数',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '上下架状态',
    is_vip_only BOOLEAN DEFAULT FALSE COMMENT '是否VIP专享',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (artist_id) REFERENCES artists(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    INDEX idx_title (title),
    INDEX idx_artist (artist_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status)
);
```

#### 5. 用户歌单表 (playlists)
```sql
CREATE TABLE playlists (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '歌单名称',
    description TEXT COMMENT '歌单描述',
    cover_url VARCHAR(500) COMMENT '封面图URL',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    song_count INT DEFAULT 0 COMMENT '歌曲数量',
    play_count BIGINT DEFAULT 0 COMMENT '播放次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_name (name)
);
```

#### 6. 歌单歌曲关联表 (playlist_songs)
```sql
CREATE TABLE playlist_songs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    playlist_id BIGINT NOT NULL,
    song_id BIGINT NOT NULL,
    sort_order INT DEFAULT 0 COMMENT '排序',
    added_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    UNIQUE KEY uk_playlist_song (playlist_id, song_id),
    INDEX idx_playlist (playlist_id),
    INDEX idx_song (song_id)
);
```

#### 7. 用户收藏表 (user_favorites)
```sql
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    song_id BIGINT NOT NULL,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_song (user_id, song_id),
    INDEX idx_user (user_id),
    INDEX idx_song (song_id)
);
```

#### 8. 评论表 (comments)
```sql
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    song_id BIGINT NOT NULL COMMENT '歌曲ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    parent_id BIGINT COMMENT '父评论ID(回复评论时使用)',
    content TEXT NOT NULL COMMENT '评论内容',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    reply_count INT DEFAULT 0 COMMENT '回复数',
    status ENUM('NORMAL', 'DELETED') DEFAULT 'NORMAL',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (song_id) REFERENCES songs(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES comments(id),
    INDEX idx_song (song_id),
    INDEX idx_user (user_id),
    INDEX idx_parent (parent_id),
    INDEX idx_created_time (created_time)
);
```

#### 9. 评论点赞表 (comment_likes)
```sql
CREATE TABLE comment_likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    comment_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_comment_user (comment_id, user_id)
);
```

#### 10. 消息表 (messages)
```sql
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sender_id BIGINT COMMENT '发送者ID(系统消息为NULL)',
    receiver_id BIGINT NOT NULL COMMENT '接收者ID',
    message_type ENUM('SYSTEM', 'PRIVATE', 'MENTION', 'FEEDBACK_REPLY') NOT NULL COMMENT '消息类型',
    title VARCHAR(200) COMMENT '消息标题',
    content TEXT NOT NULL COMMENT '消息内容',
    related_id BIGINT COMMENT '关联ID(如评论ID、歌曲ID等)',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_type (message_type),
    INDEX idx_read (is_read)
);
```

#### 11. 用户关注表 (user_follows)
```sql
CREATE TABLE user_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    following_id BIGINT NOT NULL COMMENT '被关注者ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_follow (follower_id, following_id),
    INDEX idx_follower (follower_id),
    INDEX idx_following (following_id)
);
```

#### 12. 电台表 (radio_stations)
```sql
CREATE TABLE radio_stations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '电台名称',
    description TEXT COMMENT '电台描述',
    cover_url VARCHAR(500) COMMENT '封面图URL',
    category_id BIGINT COMMENT '分类ID',
    play_count BIGINT DEFAULT 0 COMMENT '播放次数',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    INDEX idx_name (name),
    INDEX idx_category (category_id)
);
```

#### 13. 电台歌曲关联表 (radio_songs)
```sql
CREATE TABLE radio_songs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    radio_id BIGINT NOT NULL,
    song_id BIGINT NOT NULL,
    sort_order INT DEFAULT 0,
    added_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (radio_id) REFERENCES radio_stations(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    INDEX idx_radio (radio_id),
    INDEX idx_song (song_id)
);
```

#### 14. 用户反馈表 (user_feedback)
```sql
CREATE TABLE user_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    feedback_type ENUM('BUG', 'SUGGESTION', 'COMPLAINT', 'OTHER') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    contact_info VARCHAR(200) COMMENT '联系方式',
    status ENUM('PENDING', 'PROCESSING', 'RESOLVED', 'CLOSED') DEFAULT 'PENDING',
    admin_reply TEXT COMMENT '管理员回复',
    admin_id BIGINT COMMENT '处理管理员ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_type (feedback_type)
);
```

### MongoDB 集合设计

#### 1. 播放记录集合 (play_records)
```javascript
{
  _id: ObjectId,
  userId: Long,           // 用户ID
  sessionId: String,      // 会话ID
  playlistType: String,   // 播放列表类型: "default", "search", "favorite", "custom", "radio"
  playlistId: Long,       // 播放列表ID(自定义歌单ID等)
  playlistName: String,   // 播放列表名称
  songs: [                // 播放列表中的歌曲
    {
      songId: Long,
      title: String,
      artist: String,
      duration: Number,
      addedTime: Date,
      playCount: Number   // 在此列表中的播放次数
    }
  ],
  currentIndex: Number,   // 当前播放歌曲索引
  isActive: Boolean,      // 是否为当前活跃播放列表
  createdTime: Date,
  updatedTime: Date
}
```

#### 2. 用户行为统计集合 (user_statistics)
```javascript
{
  _id: ObjectId,
  userId: Long,
  date: String,           // 日期 YYYY-MM-DD
  listenTime: Number,     // 当日听歌时长(秒)
  playCount: Number,      // 当日播放次数
  favoriteCount: Number,  // 当日收藏次数
  searchCount: Number,    // 当日搜索次数
  createdTime: Date,
  updatedTime: Date
}
```

## 项目结构

```
src/main/java/org/example/sb_free_music/
├── SbFreeMusicApplication.java          # 启动类
├── aspect/                              # AOP切面
├── config/                              # 配置类
│   ├── properties/                     # 配置属性类
│   ├── SecurityConfig.java             # 安全配置
│   ├── RedisConfig.java                # Redis配置
│   ├── MongoConfig.java                # MongoDB配置
│   ├── WebSocketConfig.java            # WebSocket配置
│   └── CorsConfig.java                 # 跨域配置
├── controller/                          # 控制器层
│   ├── user/                           # 用户相关接口
│   ├── music/                          # 音乐相关接口
│   ├── admin/                          # 管理员接口
│   └── common/                         # 通用接口
│       └── DatabaseTestController.java # 数据库连接测试
├── service/                            # 服务层
│   ├── impl/                           # 服务实现
│   └── interfaces/                     # 服务接口
├── server/                             # 服务层（备用）
│   └── impl/                           # 服务实现
├── mapper/                             # MyBatis映射器
├── model/                              # 数据模型
│   ├── entity/                         # 实体类
│   │   ├── mysql/                      # MySQL实体
│   │   │   └── User.java               # 用户实体
│   │   └── mongo/                      # MongoDB实体
│   │       └── PlayRecord.java         # 播放记录实体
│   ├── dto/                            # 数据传输对象
│   └── vo/                             # 视图对象
│       └── ApiResponse.java            # 统一响应格式
├── util/                               # 工具类
├── exception/                          # 异常处理
├── security/                           # 安全相关
├── listener/                           # 事件监听器
└── scheduler/                          # 定时任务
```

### 配置文件结构

```
src/main/resources/
├── application.yaml                     # 主配置文件
├── application-dev.yaml                # 开发环境配置
├── application-prod.yaml               # 生产环境配置
├── mapper/                             # MyBatis映射文件
└── static/                             # 静态资源

项目根目录/
├── .env                                # 环境变量配置（敏感信息）
├── docs/                               # 项目文档
│   ├── Docker环境搭建指南.md
│   ├── 数据库连接配置指南.md
│   └── 后端服务端开发文档.md
└── docker-compose.yml                  # Docker编排文件（可选）
```

## 核心功能模块

### 1. 用户认证与授权模块
- JWT Token生成与验证
- 密码加密(BCrypt)
- 用户权限管理
- 会话管理(Redis)

### 2. 音乐管理模块
- 歌曲CRUD操作
- 歌手管理
- 分类管理
- 文件上传处理

### 3. 播放列表管理模块
- 默认播放列表(MongoDB)
- 用户自定义歌单
- 收藏列表
- 电台管理

### 4. 评论系统模块
- 评论发布
- 评论回复(父子评论)
- @用户功能
- 评论点赞

### 5. 消息推送模块
- WebSocket实时消息
- 系统消息
- 私信功能
- @消息通知

### 6. 用户等级系统模块
- 听歌时长统计
- 等级计算
- 称号解锁

### 7. 数据统计模块
- 播放统计
- 用户行为分析
- 热门排行榜

## 开发阶段规划

### 第一阶段：基础框架搭建
1. 项目初始化配置
2. 数据库连接配置
3. 基础实体类创建
4. 统一响应格式定义

### 第二阶段：用户系统开发
1. 用户注册登录
2. JWT认证
3. 用户信息管理
4. 权限控制

### 第三阶段：音乐核心功能
1. 歌曲管理
2. 播放列表功能
3. 搜索功能
4. 收藏功能

### 第四阶段：社交功能
1. 评论系统
2. 用户关注
3. 消息系统
4. @功能

### 第五阶段：高级功能
1. 等级系统
2. 电台功能
3. 数据统计
4. 管理后台

### 第六阶段：性能优化
1. Redis缓存
2. 数据库优化
3. 接口性能优化
4. 监控告警

## 接口设计规范

### 统一响应格式
```java
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": 1640995200000
}
```

### 错误码定义
- 200: 成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 下一步开发指导

1. **首先完成基础配置**：数据库连接、Redis配置、安全配置
2. **创建基础实体类**：根据数据库表结构创建对应的实体类
3. **实现用户认证**：注册、登录、JWT验证
4. **逐步添加业务功能**：按照阶段规划逐步开发

如果您在开发过程中遇到具体问题，可以随时向我咨询！
