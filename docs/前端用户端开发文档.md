# Free Music 前端用户端开发文档

## 项目概述
Free Music 用户端是一个现代化的音乐播放网站，提供音乐搜索、播放、收藏、社交等功能。

## 技术栈建议
- **框架**: Vue 3 + TypeScript 或 React 18 + TypeScript
- **UI组件库**: Element Plus (Vue) 或 Ant Design (React)
- **状态管理**: Pinia (Vue) 或 Redux Toolkit (React)
- **路由**: Vue Router 4 或 React Router 6
- **HTTP客户端**: Axios
- **音频播放**: Howler.js 或 HTML5 Audio API
- **实时通信**: Socket.io-client
- **构建工具**: Vite
- **CSS预处理器**: SCSS/SASS

## 页面结构设计

### 主要页面
1. **登录注册页** (`/login`, `/register`)
2. **首页** (`/`)
3. **搜索页** (`/search`)
4. **歌曲详情页** (`/song/:id`)
5. **个人中心** (`/profile`)
6. **我的收藏** (`/favorites`)
7. **我的歌单** (`/playlists`)
8. **消息中心** (`/messages`)
9. **设置页** (`/settings`)

### 布局组件
- **顶部导航栏** (Header)
- **侧边栏** (Sidebar)
- **底部播放器** (Player)
- **主内容区** (Main Content)

## 功能模块详细设计

### 1. 用户认证模块

#### 登录页面功能
- 用户名/邮箱登录
- 密码登录
- 记住登录状态
- 忘记密码链接
- 注册页面跳转
- 第三方登录预留接口(QQ登录)

#### 注册页面功能
- 用户名注册
- 邮箱注册
- 密码设置(强度验证)
- 用户协议确认
- 验证码验证(预留)

#### 认证状态管理
```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  loginTime: number;
  expiresIn: number;
}
```

### 2. 音乐播放模块

#### 播放器组件功能
- 播放/暂停控制
- 上一首/下一首
- 进度条拖拽
- 音量控制
- 播放模式切换(顺序、随机、单曲循环)
- 歌词显示
- 播放列表显示/隐藏

#### 播放列表管理
```typescript
interface PlaylistState {
  currentPlaylist: Song[];
  currentIndex: number;
  playMode: 'sequence' | 'random' | 'repeat';
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
}
```

#### 播放逻辑
- **默认播放列表**: 用户单独点击歌曲时添加到默认列表
- **列表播放**: 点击"播放全部"时替换整个播放列表
- **列表切换**: 在不同列表间切换播放逻辑
- **播放记录**: 实时同步到后端MongoDB

### 3. 搜索模块

#### 搜索功能
- 实时搜索建议
- 搜索历史记录
- 热门搜索词
- 搜索结果分类(歌曲、歌手、歌单)
- 搜索结果排序

#### 搜索结果页面
- 歌曲列表展示
- 批量操作(播放全部、添加到歌单)
- 分页加载
- 筛选功能(分类、歌手等)

### 4. 歌曲管理模块

#### 歌曲列表组件
- 歌曲信息展示(封面、标题、歌手、时长)
- 播放按钮
- 收藏按钮
- 添加到歌单按钮
- 下载按钮(VIP功能)
- 更多操作菜单

#### 收藏功能
- 收藏/取消收藏
- 收藏列表管理
- 收藏状态同步

#### 歌单功能
- 创建自定义歌单
- 歌单编辑(名称、描述、封面)
- 添加/移除歌曲
- 歌单分享
- 歌单排序

### 5. 社交功能模块

#### 评论系统
- 评论发布
- 评论回复
- 评论点赞
- @用户功能
- 评论排序(时间、热度)
- 评论分页加载

#### 用户关注
- 关注/取消关注用户
- 关注列表
- 粉丝列表
- 互相关注状态显示

#### 消息系统
- 消息列表
- 消息分类(系统消息、@消息、私信)
- 消息已读/未读状态
- 实时消息推送(WebSocket)

### 6. 个人中心模块

#### 用户信息
- 头像上传
- 昵称修改
- 个人简介
- 用户等级显示
- 听歌统计

#### 用户等级系统
- 等级进度条
- 称号展示
- 等级特权说明
- 听歌时长统计

#### 设置功能
- 密码修改
- 邮箱绑定
- 隐私设置
- 播放设置
- 通知设置

### 7. VIP功能模块

#### VIP特权
- 下载歌曲权限检查
- VIP专享歌曲播放
- 无广告播放
- 高品质音频
- VIP标识显示

#### 充值功能(预留)
- 充值页面
- 支付宝沙箱集成
- 充值记录
- 会员到期提醒

## 组件设计

### 通用组件
1. **SongItem** - 歌曲列表项组件
2. **PlaylistCard** - 歌单卡片组件
3. **UserAvatar** - 用户头像组件
4. **LoadingSpinner** - 加载动画组件
5. **Modal** - 模态框组件
6. **Toast** - 消息提示组件
7. **Pagination** - 分页组件

### 业务组件
1. **MusicPlayer** - 音乐播放器
2. **SearchBar** - 搜索栏
3. **CommentList** - 评论列表
4. **PlaylistManager** - 播放列表管理器
5. **UserCard** - 用户信息卡片
6. **MessageCenter** - 消息中心

## 状态管理设计

### 全局状态
```typescript
interface RootState {
  auth: AuthState;           // 认证状态
  player: PlayerState;       // 播放器状态
  user: UserState;          // 用户信息
  music: MusicState;        // 音乐数据
  message: MessageState;    // 消息状态
  ui: UIState;             // UI状态
}
```

### 本地存储
- **Token**: localStorage
- **用户偏好设置**: localStorage
- **播放历史**: sessionStorage
- **搜索历史**: localStorage

## API接口对接

### 接口封装
```typescript
// api/auth.ts
export const authAPI = {
  login: (data: LoginData) => request.post('/auth/login', data),
  register: (data: RegisterData) => request.post('/auth/register', data),
  logout: () => request.post('/auth/logout'),
  refreshToken: () => request.post('/auth/refresh'),
};

// api/music.ts
export const musicAPI = {
  search: (keyword: string, page: number) => request.get('/music/search', { params: { keyword, page } }),
  getSongDetail: (id: number) => request.get(`/music/songs/${id}`),
  getRecommendations: () => request.get('/music/recommendations'),
};
```

### 请求拦截器
- 自动添加Token
- 请求参数处理
- 错误统一处理
- Loading状态管理

### 响应拦截器
- Token过期处理
- 错误消息提示
- 数据格式统一处理

## 实时通信

### WebSocket连接
```typescript
class WebSocketService {
  private socket: Socket | null = null;
  
  connect(token: string) {
    this.socket = io(WS_URL, {
      auth: { token }
    });
    
    this.socket.on('message', this.handleMessage);
    this.socket.on('notification', this.handleNotification);
  }
  
  private handleMessage = (data: any) => {
    // 处理消息
  };
  
  private handleNotification = (data: any) => {
    // 处理通知
  };
}
```

### 消息类型
- **新消息通知**
- **@提醒**
- **系统通知**
- **在线状态更新**

## 性能优化

### 代码分割
- 路由懒加载
- 组件懒加载
- 第三方库按需引入

### 缓存策略
- 接口数据缓存
- 图片懒加载
- 音频预加载

### 用户体验优化
- 骨架屏加载
- 虚拟滚动(长列表)
- 防抖节流
- 错误边界处理

## 响应式设计

### 断点设计
- **移动端**: < 768px
- **平板**: 768px - 1024px
- **桌面端**: > 1024px

### 适配策略
- 弹性布局(Flexbox/Grid)
- 媒体查询
- 相对单位(rem/em/%)
- 移动端优先设计

## 开发规范

### 代码规范
- ESLint + Prettier
- TypeScript严格模式
- 组件命名规范
- 文件组织规范

### Git规范
- 分支管理策略
- 提交信息规范
- 代码审查流程

## 测试策略

### 单元测试
- 组件测试
- 工具函数测试
- API接口测试

### 集成测试
- 页面功能测试
- 用户流程测试

### E2E测试
- 关键业务流程
- 跨浏览器兼容性

## 部署方案

### 构建配置
- 环境变量配置
- 构建优化
- 资源压缩

### 部署策略
- CDN部署
- 静态资源缓存
- 版本管理

## 开发阶段规划

### 第一阶段：项目搭建
1. 项目初始化
2. 基础组件开发
3. 路由配置
4. 状态管理配置

### 第二阶段：核心功能
1. 用户认证
2. 音乐播放器
3. 搜索功能
4. 基础音乐功能

### 第三阶段：社交功能
1. 评论系统
2. 用户关注
3. 消息系统

### 第四阶段：高级功能
1. 个人中心完善
2. VIP功能
3. 性能优化
4. 移动端适配

### 第五阶段：测试与部署
1. 功能测试
2. 性能测试
3. 部署上线
4. 监控告警

## 注意事项

1. **音频版权**: 确保音频资源的合法使用
2. **性能监控**: 关注页面加载速度和音频播放性能
3. **用户体验**: 重视播放器的流畅性和响应速度
4. **安全性**: 防止XSS攻击，保护用户数据
5. **兼容性**: 确保主流浏览器兼容性

如果您在前端开发过程中遇到具体问题，可以随时向我咨询！
