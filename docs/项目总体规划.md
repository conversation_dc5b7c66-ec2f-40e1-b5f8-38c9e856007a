# Free Music 项目总体规划

## 项目概述
Free Music 是一个功能完整的在线音乐平台，包含用户端、管理员端和后端服务。项目采用前后端分离架构，支持音乐播放、社交互动、用户管理等核心功能。

## 技术架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户前端      │    │   管理员前端    │    │   移动端APP     │
│   (Vue3/React)  │    │   (Vue3/React)  │    │   (预留)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Nginx/Kong)  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Spring Boot 3  │
                    │   后端服务      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │    MongoDB      │
│   (主数据库)    │    │   (缓存/会话)   │    │   (播放记录)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术选型对比

#### 前端框架选择
| 特性 | Vue 3 | React 18 |
|------|-------|----------|
| 学习曲线 | 较平缓 | 较陡峭 |
| 生态系统 | 丰富 | 非常丰富 |
| 性能 | 优秀 | 优秀 |
| TypeScript支持 | 优秀 | 优秀 |
| 推荐指数 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

**建议**: 考虑到您是零基础，推荐使用 Vue 3，学习曲线更平缓。

## 数据库设计补充

### 索引优化策略
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_status ON users(account_status);

-- 歌曲表索引
CREATE INDEX idx_songs_title ON songs(title);
CREATE INDEX idx_songs_artist_status ON songs(artist_id, status);
CREATE INDEX idx_songs_category_status ON songs(category_id, status);
CREATE INDEX idx_songs_play_count ON songs(play_count DESC);
CREATE INDEX idx_songs_created_time ON songs(created_time DESC);

-- 评论表索引
CREATE INDEX idx_comments_song_time ON comments(song_id, created_time DESC);
CREATE INDEX idx_comments_user_time ON comments(user_id, created_time DESC);
CREATE INDEX idx_comments_parent ON comments(parent_id);

-- 播放列表索引
CREATE INDEX idx_playlist_songs_playlist ON playlist_songs(playlist_id, sort_order);
CREATE INDEX idx_user_favorites_user ON user_favorites(user_id, created_time DESC);
```

### 数据库分表策略(预留)
```sql
-- 当数据量增长时，可考虑分表
-- 按时间分表：comments_2024_01, comments_2024_02...
-- 按用户ID分表：user_favorites_0, user_favorites_1...
```

### 缓存策略设计

#### Redis 缓存结构
```
# 用户会话
session:user:{userId} -> {userInfo, permissions, loginTime}

# 歌曲信息缓存
song:info:{songId} -> {songDetail}
song:hot:list -> [songId1, songId2, ...] (热门歌曲列表)

# 用户播放列表缓存
playlist:user:{userId} -> {currentPlaylist}

# 搜索缓存
search:keyword:{keyword} -> {searchResults}
search:hot:keywords -> [keyword1, keyword2, ...] (热搜关键词)

# 统计数据缓存
stats:daily:{date} -> {playCount, userCount, ...}
stats:song:play:{songId} -> playCount

# 消息队列
queue:messages -> [message1, message2, ...]
```

## 用户等级系统详细设计

### 等级计算规则
```typescript
interface UserLevel {
  level: string;        // K1, K2, K3...
  title: string;        // 称号
  minListenTime: number; // 最低听歌时长(小时)
  maxListenTime: number; // 最高听歌时长(小时)
  privileges: string[]; // 特权列表
}

const levelConfig: UserLevel[] = [
  { level: 'K1', title: '初出茅庐', minListenTime: 0, maxListenTime: 10, privileges: ['basic'] },
  { level: 'K2', title: '音乐爱好者', minListenTime: 10, maxListenTime: 50, privileges: ['basic', 'comment'] },
  { level: 'K3', title: '资深听众', minListenTime: 50, maxListenTime: 100, privileges: ['basic', 'comment', 'playlist'] },
  { level: 'K4', title: '音乐达人', minListenTime: 100, maxListenTime: 300, privileges: ['basic', 'comment', 'playlist', 'follow'] },
  { level: 'K5', title: '大师', minListenTime: 300, maxListenTime: 1000, privileges: ['basic', 'comment', 'playlist', 'follow', 'radio'] },
  { level: 'K6', title: '宗师', minListenTime: 1000, maxListenTime: Infinity, privileges: ['all'] }
];
```

### 称号系统
```typescript
interface UserTitle {
  id: string;
  name: string;
  description: string;
  unlockCondition: {
    type: 'LISTEN_TIME' | 'PLAY_COUNT' | 'FAVORITE_COUNT' | 'COMMENT_COUNT';
    value: number;
  };
  icon: string;
  rarity: 'COMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
}
```

## 播放列表逻辑详细设计

### 播放列表类型
```typescript
enum PlaylistType {
  DEFAULT = 'default',      // 默认播放列表(临时)
  SEARCH = 'search',        // 搜索结果列表
  FAVORITE = 'favorite',    // 收藏列表
  CUSTOM = 'custom',        // 用户自建歌单
  RADIO = 'radio',          // 电台列表
  RECOMMEND = 'recommend'   // 推荐列表
}
```

### 播放逻辑流程图
```
用户操作 → 判断操作类型
    ├── 单曲播放 → 添加到默认列表 → 播放
    ├── 播放全部 → 替换当前列表 → 播放第一首
    └── 列表内播放 → 保持当前列表 → 播放指定歌曲
```

### MongoDB 播放记录设计
```javascript
// 播放记录集合优化
{
  _id: ObjectId,
  userId: Long,
  sessionId: String,      // 浏览器会话ID
  playlists: {            // 多个播放列表
    default: {
      type: "default",
      songs: [...],
      currentIndex: 0,
      lastPlayTime: Date
    },
    search: {
      type: "search",
      keyword: "周杰伦",
      songs: [...],
      currentIndex: 0,
      lastPlayTime: Date
    },
    custom_123: {
      type: "custom",
      playlistId: 123,
      songs: [...],
      currentIndex: 0,
      lastPlayTime: Date
    }
  },
  activePlaylist: "default", // 当前活跃的播放列表
  createdTime: Date,
  updatedTime: Date,
  // TTL索引，会话结束后自动删除
  expireAt: Date
}
```

## 消息推送系统设计

### WebSocket 消息类型
```typescript
interface WebSocketMessage {
  type: 'MESSAGE' | 'NOTIFICATION' | 'MENTION' | 'SYSTEM';
  data: {
    id: string;
    title?: string;
    content: string;
    sender?: UserInfo;
    relatedId?: number;
    timestamp: number;
  };
}
```

### 消息推送流程
```
用户A @用户B → 后端检测@ → 创建消息记录 → WebSocket推送 → 用户B收到通知
```

## 文件存储方案

### 音频文件存储
- **本地存储**: 开发阶段使用
- **云存储**: 生产环境推荐(阿里云OSS、腾讯云COS)
- **CDN加速**: 提高音频加载速度

### 文件上传流程
```
选择文件 → 前端验证 → 生成上传Token → 直传云存储 → 返回文件URL → 保存到数据库
```

## 安全设计

### 密码安全
```java
// BCrypt加密示例
String password = "userPassword";
String hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt(12));
```

### JWT Token设计
```typescript
interface JWTPayload {
  userId: number;
  username: string;
  userType: 'NORMAL' | 'VIP' | 'SVIP';
  permissions: string[];
  iat: number;  // 签发时间
  exp: number;  // 过期时间
}
```

### 接口安全
- **参数验证**: 使用Bean Validation
- **SQL注入防护**: 使用MyBatis-Plus预编译
- **XSS防护**: 前端输入过滤
- **CSRF防护**: 使用CSRF Token

## 性能优化策略

### 数据库优化
1. **索引优化**: 根据查询模式建立合适索引
2. **查询优化**: 避免N+1查询，使用批量查询
3. **分页优化**: 使用游标分页替代OFFSET
4. **读写分离**: 主从数据库分离

### 缓存策略
1. **多级缓存**: 浏览器缓存 → CDN缓存 → Redis缓存 → 数据库
2. **缓存更新**: 使用缓存穿透、缓存雪崩防护
3. **热点数据**: 预热热门歌曲数据

### 前端优化
1. **代码分割**: 路由懒加载、组件懒加载
2. **资源优化**: 图片压缩、音频压缩
3. **网络优化**: HTTP/2、资源预加载

## 监控告警

### 系统监控指标
- **服务器指标**: CPU、内存、磁盘、网络
- **应用指标**: 响应时间、错误率、吞吐量
- **业务指标**: 用户活跃度、播放量、注册量

### 告警策略
- **错误率告警**: 错误率超过5%
- **响应时间告警**: 平均响应时间超过2秒
- **资源告警**: CPU使用率超过80%

## 部署架构

### 开发环境
```
开发机 → Git → 自动构建 → 开发服务器
```

### 生产环境
```
Git → CI/CD Pipeline → 测试 → 构建 → 部署 → 监控
```

### 容器化部署(推荐)
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: free-music-backend:latest
    ports:
      - "8080:8080"
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
  redis:
    image: redis:7-alpine
  mongodb:
    image: mongo:6
```

## 开发时间规划

### 总体时间估算(基于零基础学习)
- **学习阶段**: 2-3周 (Spring Boot基础学习)
- **第一阶段**: 2-3周 (基础框架搭建)
- **第二阶段**: 3-4周 (用户系统和音乐核心功能)
- **第三阶段**: 2-3周 (社交功能)
- **第四阶段**: 2-3周 (高级功能)
- **第五阶段**: 1-2周 (测试和优化)

### 学习建议
1. **先学习Spring Boot基础**: 依赖注入、MVC模式、数据访问
2. **实践驱动学习**: 边做项目边学习相关技术
3. **循序渐进**: 从简单功能开始，逐步增加复杂度
4. **多查文档**: 善用官方文档和社区资源

## 风险评估

### 技术风险
- **学习曲线**: 零基础学习Spring Boot需要时间
- **性能问题**: 大数据量时的性能优化
- **安全风险**: 用户数据安全和系统安全

### 解决方案
- **分阶段开发**: 降低复杂度
- **代码审查**: 确保代码质量
- **测试驱动**: 编写充分的测试用例

## 后续扩展规划

### 功能扩展
1. **移动端APP**: React Native或Flutter
2. **AI推荐**: 基于用户行为的智能推荐
3. **社区功能**: 用户动态、音乐分享
4. **直播功能**: 音乐直播、电台直播

### 技术升级
1. **微服务架构**: 服务拆分和治理
2. **大数据分析**: 用户行为分析
3. **机器学习**: 个性化推荐算法
4. **区块链**: 音乐版权保护

这个项目规划为您提供了完整的开发路线图。建议您先从后端基础框架开始，逐步实现各个功能模块。在开发过程中遇到任何问题，都可以随时向我咨询！
