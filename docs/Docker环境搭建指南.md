# Docker环境搭建指南

## 概述
本文档提供了在Windows环境下使用Docker Desktop搭建MySQL 8.0.37、MongoDB 7.0.14和Redis 7.2.5的完整流程。

## 推荐版本
- **MySQL**: 8.0.37 (LTS版本，稳定可靠)
- **MongoDB**: 7.0.14 (社区版，免费使用，功能完整)
- **Redis**: 7.2.5 (最新稳定版，性能优异)

## 目录结构

在D盘创建以下目录结构：

```
D:/docker-data/
├── mysql/
│   ├── data/           # MySQL数据存储目录
│   ├── conf/           # MySQL配置文件目录(映射到/etc/mysql/conf.d)
│   │   └── custom.cnf  # MySQL自定义配置文件
│   └── logs/           # MySQL日志目录
├── mongodb/
│   ├── data/           # MongoDB数据存储目录
│   ├── conf/           # MongoDB配置文件目录
│   │   └── mongod.conf # MongoDB配置文件
│   └── logs/           # MongoDB日志目录
└── redis/
    ├── data/           # Redis数据存储目录
    ├── conf/           # Redis配置文件目录
    │   └── redis.conf  # Redis配置文件
    └── logs/           # Redis日志目录
```

## 配置文件

### 1. MySQL配置文件 (D:/docker-data/mysql/conf/custom.cnf)

> **重要说明**: MySQL配置文件放在`D:/docker-data/mysql/conf.d/`目录下，该目录会被映射到容器内的`/etc/mysql/conf.d/`。MySQL会自动加载该目录下所有的`.cnf`文件，推荐使用`custom.cnf`作为主配置文件名。

```ini
[mysqld]
# ===== 核心配置 =====
user = mysql
port = 3306                     # 使用标准端口
bind-address = 0.0.0.0         # 允许外部连接
server-id = 1                   # 服务器ID
datadir = /var/lib/mysql
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# ===== 字符集配置 =====
character-set-server = utf8mb4
collation-server = utf8mb4_bin  # 使用二进制排序规则，区分大小写
init_connect = 'SET NAMES utf8mb4'

# ===== 连接配置 =====
max_connections = 200           # 适中的连接数，避免资源浪费
max_connect_errors = 6000       # 最大连接错误数
thread_cache_size = 10          # 线程缓存大小
skip-name-resolve               # 跳过DNS解析，提升性能
skip_symbolic_links = ON        # 安全设置

# ===== 日志配置 =====
log_error = /var/log/mysql/error.log
log_error_verbosity = 3         # 详细错误日志
slow_query_log = 1              # 启用慢查询日志
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1             # 慢查询阈值1秒（更敏感）
log_queries_not_using_indexes = 1  # 记录未使用索引的查询
general_log = 0                 # 关闭通用日志（性能考虑）
log_slow_rate_limit = 10        # 慢查询采样率

# ===== 二进制日志设置 =====
log-bin = mysql-bin
binlog_format = ROW             # 行级复制格式
expire_logs_days = 7            # 日志保留7天

# ===== 性能优化配置 =====
innodb_buffer_pool_size = 1G    # InnoDB缓冲池大小
innodb_log_file_size = 256M     # 日志文件大小
innodb_log_buffer_size = 32M    # 日志缓冲区大小
innodb_flush_log_at_trx_commit = 2  # SSD优化设置
innodb_io_capacity = 2000       # IO容量设置
innodb_read_io_threads = 8      # 读IO线程数
innodb_write_io_threads = 8     # 写IO线程数
innodb_file_per_table = 1       # 每个表独立表空间
table_open_cache = 2000         # 表缓存大小
query_cache_type = 0            # 禁用查询缓存（MySQL 8.0推荐）

# ===== 安全配置 =====
default_authentication_plugin = mysql_native_password
local_infile = OFF              # 禁用本地文件加载
secure_file_priv = /var/lib/mysql-files  # 限制文件操作目录
default_time_zone = '+8:00'     # 设置时区

# ===== 密码策略 =====
validate_password.policy = MEDIUM
validate_password.length = 12   # 密码最小长度
validate_password.mixed_case_count = 1
validate_password.number_count = 1
validate_password.special_char_count = 1
default_password_lifetime = 90  # 密码有效期90天

[mysql]
default-character-set = utf8mb4
prompt = "\\u@\\h : \\d \\r:\\m:\\s> "  # 增强命令行提示符

[client]
default-character-set = utf8mb4
```

### 2. MongoDB配置文件 (D:/docker-data/mongodb/conf/mongod.conf)

```yaml
# 网络设置
net:
  port: 27017                   # MongoDB端口
  bindIp: 0.0.0.0              # 绑定所有IP地址

# 存储设置
storage:
  dbPath: /data/db              # 数据库文件存储路径
  wiredTiger:
    engineConfig:
      cacheSizeGB: 1            # WiredTiger缓存大小(GB)
    collectionConfig:
      blockCompressor: snappy   # 集合数据压缩算法
    indexConfig:
      prefixCompression: true   # 索引前缀压缩

# 系统日志设置
systemLog:
  destination: file
  path: /var/log/mongodb/mongod.log
  logAppend: true               # 追加日志而不是覆盖
  logRotate: rename             # 日志轮转方式
  verbosity: 0                  # 日志详细级别 (0-5)

# 进程管理
processManagement:
  fork: false                   # Docker中不需要后台运行

# 安全设置
security:
  authorization: enabled        # 启用认证

# 操作分析
operationProfiling:
  slowOpThresholdMs: 100        # 慢操作阈值(毫秒)
  mode: slowOp                  # 只记录慢操作

# 复制集设置 (可选，单机部署可注释掉)
# replication:
#   replSetName: "rs0"          # 复制集名称
```

### 3. Redis配置文件 (D:/docker-data/redis/conf/redis.conf)

> **重要说明**: Redis配置文件不支持行内注释，所有注释必须单独成行。配置项和值之间用空格分隔。

```conf
# ===== 网络配置 =====
# 绑定所有网络接口，允许外部连接
bind 0.0.0.0
# Redis服务端口
port 6379
# 关闭保护模式（容器部署必须）
protected-mode no
# 客户端空闲超时时间(秒)，0表示永不超时
timeout 0

# ===== 通用配置 =====
# Docker中不需要后台运行
daemonize no
# 进程ID文件路径
pidfile /var/run/redis_6379.pid

# ===== 日志配置 =====
# 日志级别: debug, verbose, notice, warning
loglevel notice
# 日志文件路径
logfile /var/log/redis/redis.log

# ===== 安全配置 =====
# 设置访问密码
requirepass "@JJ193495.x"

# ===== 持久化配置 =====
# 数据文件存储目录
dir /data

# RDB持久化规则
# 900秒内至少1个key变化时保存
save 900 1
# 300秒内至少10个key变化时保存
save 300 10
# 60秒内至少10000个key变化时保存
save 60 10000

# RDB文件名
dbfilename dump.rdb

# 启用AOF持久化
appendonly yes
# AOF文件名
appendfilename "appendonly.aof"
# AOF同步策略: always, everysec, no
appendfsync everysec

# ===== 内存管理 =====
# 最大内存使用量
maxmemory 2gb
# 内存不足时的淘汰策略
maxmemory-policy volatile-lru

# ===== 性能优化 =====
# IO线程数（根据CPU核心数调整）
io-threads 4
# 启用IO线程处理读操作
io-threads-do-reads yes

# ===== 慢日志配置 =====
# 慢查询阈值(微秒)
slowlog-log-slower-than 10000
# 慢查询日志最大长度
slowlog-max-len 128

# ===== 客户端配置 =====
# 最大客户端连接数
maxclients 10000
```

## Docker命令执行流程

### 第一步：创建目录结构

```powershell
# 在PowerShell中执行
mkdir D:\docker-data\mysql\data, D:\docker-data\mysql\conf, D:\docker-data\mysql\logs
mkdir D:\docker-data\mongodb\data, D:\docker-data\mongodb\conf, D:\docker-data\mongodb\logs  
mkdir D:\docker-data\redis\data, D:\docker-data\redis\conf, D:\docker-data\redis\logs
```

### 第二步：创建配置文件

将上述配置文件内容分别保存到对应的配置文件中。

### 第三步：拉取Docker镜像

```bash
# 拉取MySQL 8.0.37镜像
docker pull mysql:8.0.37

# 拉取MongoDB 7.0.14镜像
docker pull mongo:7.0.14

# 拉取Redis 7.2.5镜像
docker pull redis:7.2.5
```

### 第四步：创建Docker网络

```bash
# 创建自定义网络，便于容器间通信
docker network create dev-network
```

### 第五步：启动容器

#### 启动MySQL容器

> **配置说明**: MySQL配置文件映射到`/etc/mysql/conf.d/`目录而不是单个文件，这样可以：
> - 支持多个配置文件的加载（MySQL会自动加载该目录下所有.cnf文件）
> - 避免覆盖MySQL官方镜像的默认配置
> - 更符合MySQL的标准配置管理方式

```bash
docker run -d \
  --name mysql-dev \
  --network dev-network \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=root123456 \
  -e MYSQL_DATABASE=sb_free_music \
  -e MYSQL_USER=dev \
  -e MYSQL_PASSWORD=dev123456 \
  -v D:/docker-data/mysql/data:/var/lib/mysql \
  -v D:/docker-data/mysql/conf:/etc/mysql/conf.d \
  -v D:/docker-data/mysql/logs:/var/log/mysql \
  --restart unless-stopped \
  mysql:8.0.37
```

#### 启动MongoDB容器

```bash
docker run -d \
  --name mongodb-dev \
  --network dev-network \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=admin123456 \
  -e MONGO_INITDB_DATABASE=sb_free_music \
  -v D:/docker-data/mongodb/data:/data/db \
  -v D:/docker-data/mongodb/conf:/etc/mongo \
  -v D:/docker-data/mongodb/logs:/var/log/mongodb \
  --restart unless-stopped \
  mongo:7.0.14 --config /etc/mongo/mongod.conf
```

#### 启动Redis容器

```bash
docker run -d \
  --name redis-dev \
  --network dev-network \
  -p 6379:6379 \
  -v D:/docker-data/redis/data:/data \
  -v D:/docker-data/redis/conf:/usr/local/etc/redis \
  -v D:/docker-data/redis/logs:/var/log/redis \
  --restart unless-stopped \
  --memory 1g \
  redis:7.2.5 redis-server /usr/local/etc/redis/redis.conf
```

## 验证安装

### 验证MySQL

```bash
# 连接MySQL
docker exec -it mysql-dev mysql -uroot -proot123456

# 查看数据库
SHOW DATABASES;
```

### 验证MongoDB

```bash
# 连接MongoDB
docker exec -it mongodb-dev mongosh -u admin -p admin123456

# 查看数据库
show dbs
```

### 验证Redis

```bash
# 连接Redis
docker exec -it redis-dev redis-cli -a your_redis_password

# 测试连接
ping
```

## 常用管理命令

```bash
# 查看所有容器状态
docker ps -a

# 停止所有服务
docker stop mysql-dev mongodb-dev redis-dev

# 启动所有服务
docker start mysql-dev mongodb-dev redis-dev

# 重启服务
docker restart mysql-dev mongodb-dev redis-dev

# 查看容器日志
docker logs mysql-dev
docker logs mongodb-dev  
docker logs redis-dev

# 进入容器
docker exec -it mysql-dev bash
docker exec -it mongodb-dev bash
docker exec -it redis-dev bash
```

## 注意事项

1. **密码安全**: 请修改配置文件中的默认密码
2. **防火墙**: 确保Windows防火墙允许相应端口通信
3. **资源配置**: 根据实际硬件情况调整内存和缓存配置
4. **数据备份**: 定期备份D:/docker-data目录下的数据
5. **日志管理**: 定期清理日志文件，避免占用过多磁盘空间

## 连接信息汇总

| 服务 | 端口 | 用户名 | 密码 | 数据库 |
|------|------|--------|------|--------|
| MySQL | 3306 | root | root123456 | sb_free_music |
| MySQL | 3306 | dev | dev123456 | sb_free_music |
| MongoDB | 27017 | admin | admin123456 | sb_free_music |
| Redis | 6379 | - | your_redis_password | - |

配置完成后，您的Spring Boot项目就可以连接这些数据库服务了。
