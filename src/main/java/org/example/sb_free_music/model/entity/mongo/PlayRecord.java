package org.example.sb_free_music.model.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 播放记录实体类 - MongoDB
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Data
@Document(collection = "play_records")
public class PlayRecord {
    
    /**
     * 记录ID
     */
    @Id
    private String id;
    
    /**
     * 用户ID
     */
    @Field("user_id")
    private Long userId;
    
    /**
     * 会话ID
     */
    @Field("session_id")
    private String sessionId;
    
    /**
     * 播放列表类型：default-默认, search-搜索, favorite-收藏, custom-自定义, radio-电台
     */
    @Field("playlist_type")
    private String playlistType;
    
    /**
     * 播放列表ID（自定义歌单ID等）
     */
    @Field("playlist_id")
    private Long playlistId;
    
    /**
     * 播放列表名称
     */
    @Field("playlist_name")
    private String playlistName;
    
    /**
     * 播放列表中的歌曲
     */
    @Field("songs")
    private List<PlaySong> songs;
    
    /**
     * 当前播放歌曲索引
     */
    @Field("current_index")
    private Integer currentIndex;
    
    /**
     * 是否为当前活跃播放列表
     */
    @Field("is_active")
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    @Field("created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @Field("updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 播放歌曲信息
     */
    @Data
    public static class PlaySong {
        
        /**
         * 歌曲ID
         */
        @Field("song_id")
        private Long songId;
        
        /**
         * 歌曲标题
         */
        @Field("title")
        private String title;
        
        /**
         * 歌手名称
         */
        @Field("artist")
        private String artist;
        
        /**
         * 歌曲时长（秒）
         */
        @Field("duration")
        private Integer duration;
        
        /**
         * 添加到播放列表的时间
         */
        @Field("added_time")
        private LocalDateTime addedTime;
        
        /**
         * 在此列表中的播放次数
         */
        @Field("play_count")
        private Integer playCount;
        
        /**
         * 歌曲封面URL
         */
        @Field("cover_url")
        private String coverUrl;
        
        /**
         * 音频文件URL
         */
        @Field("file_url")
        private String fileUrl;
    }
    
    /**
     * 播放列表类型常量
     */
    public static class PlaylistType {
        public static final String DEFAULT = "default";
        public static final String SEARCH = "search";
        public static final String FAVORITE = "favorite";
        public static final String CUSTOM = "custom";
        public static final String RADIO = "radio";
    }
    
    /**
     * 获取当前播放的歌曲
     */
    public PlaySong getCurrentSong() {
        if (songs == null || songs.isEmpty() || currentIndex == null 
            || currentIndex < 0 || currentIndex >= songs.size()) {
            return null;
        }
        return songs.get(currentIndex);
    }
    
    /**
     * 切换到下一首歌曲
     */
    public boolean nextSong() {
        if (songs == null || songs.isEmpty()) {
            return false;
        }
        if (currentIndex == null || currentIndex >= songs.size() - 1) {
            currentIndex = 0; // 循环播放
        } else {
            currentIndex++;
        }
        this.updatedTime = LocalDateTime.now();
        return true;
    }
    
    /**
     * 切换到上一首歌曲
     */
    public boolean previousSong() {
        if (songs == null || songs.isEmpty()) {
            return false;
        }
        if (currentIndex == null || currentIndex <= 0) {
            currentIndex = songs.size() - 1; // 循环播放
        } else {
            currentIndex--;
        }
        this.updatedTime = LocalDateTime.now();
        return true;
    }
    
    /**
     * 增加当前歌曲播放次数
     */
    public void incrementCurrentSongPlayCount() {
        PlaySong currentSong = getCurrentSong();
        if (currentSong != null) {
            if (currentSong.getPlayCount() == null) {
                currentSong.setPlayCount(1);
            } else {
                currentSong.setPlayCount(currentSong.getPlayCount() + 1);
            }
            this.updatedTime = LocalDateTime.now();
        }
    }
}
