package org.example.sb_free_music.model.entity.mysql;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_users")
public class User {
    
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    
    /**
     * 加密密码
     */
    @TableField("password")
    private String password;
    
    /**
     * 邮箱
     */
    @TableField("email")
    private String email;
    
    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;
    
    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;
    
    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;
    
    /**
     * 用户类型：NORMAL-普通用户, VIP-VIP用户, SVIP-超级VIP
     */
    @TableField("user_type")
    private UserType userType;
    
    /**
     * 用户等级
     */
    @TableField("user_level")
    private String userLevel;
    
    /**
     * 用户称号
     */
    @TableField("user_title")
    private String userTitle;
    
    /**
     * 总听歌时间(秒)
     */
    @TableField("total_listen_time")
    private Long totalListenTime;
    
    /**
     * 账号状态：NORMAL-正常, BANNED-封禁
     */
    @TableField("account_status")
    private AccountStatus accountStatus;
    
    /**
     * 封禁结束时间
     */
    @TableField("ban_end_time")
    private LocalDateTime banEndTime;
    
    /**
     * QQ登录OpenID
     */
    @TableField("qq_openid")
    private String qqOpenid;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    /**
     * 逻辑删除标记：0-未删除, 1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        NORMAL, VIP, SVIP
    }
    
    /**
     * 账号状态枚举
     */
    public enum AccountStatus {
        NORMAL, BANNED
    }
    
    /**
     * 判断用户是否被封禁
     */
    public boolean isBanned() {
        return AccountStatus.BANNED.equals(this.accountStatus) 
            && (this.banEndTime == null || this.banEndTime.isAfter(LocalDateTime.now()));
    }
    
    /**
     * 判断用户是否为VIP
     */
    public boolean isVip() {
        return UserType.VIP.equals(this.userType) || UserType.SVIP.equals(this.userType);
    }
    
    /**
     * 判断用户是否为超级VIP
     */
    public boolean isSvip() {
        return UserType.SVIP.equals(this.userType);
    }
}
