package org.example.sb_free_music.controller;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    private StringRedisTemplate redisTemplate;

    public void RedisTestController(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @GetMapping("/test")
    public String testRedis() {
        redisTemplate.opsForValue().set("hello", "world");
        return redisTemplate.opsForValue().get("hello");
    }
}