package org.example.sb_free_music.controller.common;

import lombok.extern.slf4j.Slf4j;
import org.example.sb_free_music.model.vo.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库连接测试控制器
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class DatabaseTestController {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    /**
     * 测试MySQL连接
     */
    @GetMapping("/mysql")
    public ApiResponse<Map<String, Object>> testMySQL() {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            result.put("status", "连接成功");
            result.put("database", connection.getCatalog());
            result.put("url", connection.getMetaData().getURL());
            result.put("driver", connection.getMetaData().getDriverName());
            result.put("version", connection.getMetaData().getDatabaseProductVersion());
            
            log.info("MySQL连接测试成功: {}", result);
            return ApiResponse.success("MySQL连接成功", result);
            
        } catch (Exception e) {
            result.put("status", "连接失败");
            result.put("error", e.getMessage());
            
            log.error("MySQL连接测试失败", e);
            return ApiResponse.error(500, "MySQL连接失败: " + e.getMessage(), result);
        }
    }
    
    /**
     * 测试Redis连接
     */
    @GetMapping("/redis")
    public ApiResponse<Map<String, Object>> testRedis() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试写入
            String testKey = "test:connection:" + System.currentTimeMillis();
            String testValue = "Hello Redis from sb_free_music!";
            redisTemplate.opsForValue().set(testKey, testValue);
            
            // 测试读取
            String retrievedValue = redisTemplate.opsForValue().get(testKey);
            
            // 清理测试数据
            redisTemplate.delete(testKey);
            
            result.put("status", "连接成功");
            result.put("testKey", testKey);
            result.put("testValue", testValue);
            result.put("retrievedValue", retrievedValue);
            result.put("writeReadSuccess", testValue.equals(retrievedValue));
            
            // 获取Redis信息
            try {
                result.put("ping", redisTemplate.getConnectionFactory().getConnection().ping());
            } catch (Exception e) {
                result.put("ping", "无法获取ping信息");
            }
            
            log.info("Redis连接测试成功: {}", result);
            return ApiResponse.success("Redis连接成功", result);
            
        } catch (Exception e) {
            result.put("status", "连接失败");
            result.put("error", e.getMessage());
            
            log.error("Redis连接测试失败", e);
            return ApiResponse.error(500, "Redis连接失败: " + e.getMessage(), result);
        }
    }
    
    /**
     * 测试MongoDB连接
     */
    @GetMapping("/mongodb")
    public ApiResponse<Map<String, Object>> testMongoDB() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试连接
            String databaseName = mongoTemplate.getDb().getName();
            
            // 测试写入和读取
            String collectionName = "connection_test";
            Map<String, Object> testDoc = new HashMap<>();
            testDoc.put("test", "Hello MongoDB from sb_free_music!");
            testDoc.put("timestamp", System.currentTimeMillis());
            
            mongoTemplate.insert(testDoc, collectionName);
            
            // 查询测试文档
            Map<String, Object> retrievedDoc = mongoTemplate.findById(testDoc.get("_id"), Map.class, collectionName);
            
            // 清理测试数据
            mongoTemplate.remove(testDoc, collectionName);
            
            result.put("status", "连接成功");
            result.put("database", databaseName);
            result.put("testCollection", collectionName);
            result.put("writeReadSuccess", retrievedDoc != null);
            
            // 获取数据库统计信息
            try {
                result.put("collections", mongoTemplate.getCollectionNames());
            } catch (Exception e) {
                result.put("collections", "无法获取集合列表");
            }
            
            log.info("MongoDB连接测试成功: {}", result);
            return ApiResponse.success("MongoDB连接成功", result);
            
        } catch (Exception e) {
            result.put("status", "连接失败");
            result.put("error", e.getMessage());
            
            log.error("MongoDB连接测试失败", e);
            return ApiResponse.error(500, "MongoDB连接失败: " + e.getMessage(), result);
        }
    }
    
    /**
     * 测试所有数据库连接
     */
    @GetMapping("/all")
    public ApiResponse<Map<String, Object>> testAllDatabases() {
        Map<String, Object> result = new HashMap<>();
        
        // 测试MySQL
        ApiResponse<Map<String, Object>> mysqlResult = testMySQL();
        result.put("mysql", mysqlResult.getData());
        
        // 测试Redis
        ApiResponse<Map<String, Object>> redisResult = testRedis();
        result.put("redis", redisResult.getData());
        
        // 测试MongoDB
        ApiResponse<Map<String, Object>> mongoResult = testMongoDB();
        result.put("mongodb", mongoResult.getData());
        
        // 统计连接状态
        boolean allSuccess = mysqlResult.isSuccess() && redisResult.isSuccess() && mongoResult.isSuccess();
        result.put("allConnected", allSuccess);
        result.put("summary", allSuccess ? "所有数据库连接正常" : "部分数据库连接异常");
        
        log.info("数据库连接测试完成，结果: {}", allSuccess ? "全部成功" : "部分失败");
        
        return allSuccess ? 
            ApiResponse.success("数据库连接测试完成", result) : 
            ApiResponse.error(500, "部分数据库连接异常", result);
    }
}
