package org.example.sb_free_music.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

/**
 * MongoDB配置类
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Slf4j
@Configuration
public class MongoConfig extends AbstractMongoClientConfiguration {
    
    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;
    
    /**
     * 获取数据库名称
     */
    @Override
    protected String getDatabaseName() {
        // 从URI中提取数据库名称
        try {
            String[] parts = mongoUri.split("/");
            if (parts.length > 3) {
                String dbPart = parts[3];
                // 移除查询参数
                if (dbPart.contains("?")) {
                    dbPart = dbPart.substring(0, dbPart.indexOf("?"));
                }
                return dbPart;
            }
        } catch (Exception e) {
            log.warn("无法从URI中提取数据库名称，使用默认值: {}", e.getMessage());
        }
        return "sb_free_music";
    }
    
    /**
     * 创建MongoDB客户端
     */
    @Override
    public MongoClient mongoClient() {
        log.info("正在创建MongoDB客户端，URI: {}", maskPassword(mongoUri));
        return MongoClients.create(mongoUri);
    }
    
    /**
     * 自定义MongoTemplate配置
     */
    @Bean
    @Override
    public MongoTemplate mongoTemplate() {
        MongoTemplate template = new MongoTemplate(mongoClient(), getDatabaseName());
        
        // 移除_class字段（MongoDB默认会添加类型信息）
        MappingMongoConverter converter = (MappingMongoConverter) template.getConverter();
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        
        log.info("MongoDB连接配置完成，数据库: {}", getDatabaseName());
        return template;
    }
    
    /**
     * 自定义转换器配置
     */
    @Bean
    @Override
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(java.util.Collections.emptyList());
    }
    
    /**
     * MongoDB映射上下文配置
     */
    @Bean
    @Override
    public MongoMappingContext mongoMappingContext() throws ClassNotFoundException {
        MongoMappingContext context = new MongoMappingContext();
        context.setInitialEntitySet(getInitialEntitySet());
        context.setSimpleTypeHolder(customConversions().getSimpleTypeHolder());
        context.setFieldNamingStrategy(org.springframework.data.mapping.model.PropertyNameFieldNamingStrategy.INSTANCE);
        return context;
    }
    
    /**
     * 掩码密码信息用于日志输出
     */
    private String maskPassword(String uri) {
        if (uri == null || !uri.contains("@")) {
            return uri;
        }
        
        try {
            // 格式: ********************************:port/database
            String[] parts = uri.split("@");
            if (parts.length >= 2) {
                String userPart = parts[0];
                String hostPart = parts[1];
                
                if (userPart.contains(":")) {
                    String[] userInfo = userPart.split(":");
                    if (userInfo.length >= 3) {
                        // mongodb://username:password -> mongodb://username:****
                        return userInfo[0] + ":" + userInfo[1] + ":****@" + hostPart;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("无法掩码URI密码: {}", e.getMessage());
        }
        
        return uri.replaceAll(":[^:@]+@", ":****@");
    }
}
