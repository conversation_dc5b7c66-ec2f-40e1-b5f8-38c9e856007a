spring:
  application:
    name: sb_free_music

  # ===== 数据源配置 =====
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # Docker MySQL连接配置 - 使用环境变量
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}
    # Docker MySQL连接URL - 更新时区和字符编码
    url: **********************************************************************************************************************************************************

    # 连接池配置（HikariCP）
    hikari:
      minimum-idle: 5              # 最小空闲连接数
      maximum-pool-size: 20        # 最大连接池大小
      connection-timeout: 30000    # 连接超时时间（毫秒）
      idle-timeout: 600000         # 空闲连接最大存活时间（毫秒）
      max-lifetime: 1800000        # 连接最大存活时间（毫秒）
      connection-test-query: SELECT 1  # 连接测试查询

  # ===== Redis配置 =====
  data:
    redis:
      # Docker Redis连接配置
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:@JJ193495.x}  # Redis访问密码
      timeout: 5000                # 连接超时时间（毫秒）
      database: 0                  # 数据库索引（默认0）

      # 连接池配置（Lettuce）
      lettuce:
        pool:
          max-active: 20           # 最大连接数
          max-idle: 10             # 最大空闲连接数
          min-idle: 5              # 最小空闲连接数
          max-wait: 5000           # 获取连接最大等待时间（毫秒）

  # ===== MongoDB配置 =====
  data:
    mongodb:
      # Docker MongoDB连接配置
      host: localhost
      port: 27017
      database: sb_free_music
      username: ${MONGO_USERNAME:admin}
      password: ${MONGO_PASSWORD:@JJ193495.x}
      authentication-database: admin

      # 连接池配置
      options:
        connections-per-host: 20     # 最大连接数
        min-connections-per-host: 5  # 最小连接数
        connect-timeout: 5000        # 连接超时时间（毫秒）
        socket-timeout: 5000         # Socket超时时间（毫秒）

mybatis-plus:
  # 实体类包路径
  type-aliases-package: org.example.sb_free_music.model.entity
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型 AUTO:数据库自增 ASSIGN_ID:雪花算法 ASSIGN_UUID:UUID
      id-type: AUTO
      # 表名前缀
      table-prefix: t_
      # 逻辑删除字段名
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # MyBatis配置项
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 关闭积极延迟加载
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许JDBC支持自动生成主键
    use-generated-keys: true
    # 自动映射行为 PARTIAL:部分映射
    auto-mapping-behavior: PARTIAL
    # 未知列映射行为 WARNING:警告
    auto-mapping-unknown-column-behavior: WARNING
    # 默认执行器类型 SIMPLE:简单执行器
    default-executor-type: SIMPLE
    # 默认语句超时时间 单位秒
    default-statement-timeout: 25
    # 默认获取数据量
    default-fetch-size: 100
    # 日志实现 - 开发环境输出SQL到控制台
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# ===== 日志配置 =====
logging:
  level:
    # 应用日志级别
    org.example.sb_free_music: DEBUG
    # MyBatis日志级别
    org.apache.ibatis: DEBUG
    # SQL日志级别
    org.apache.ibatis.logging.jdbc: DEBUG
    # HikariCP连接池日志
    com.zaxxer.hikari: DEBUG
    # Redis日志级别
    org.springframework.data.redis: DEBUG

# ===== 服务器配置 =====
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true